import { api } from "encore.dev/api"
import { secret } from "encore.dev/config"
import { Energy, ListEnergyResponse, FilterQuery, EnergySourceType, EnergyUnit, EmissionScope, TimePeriod } from "../shared/types"
import { DatabaseConnection, DatabaseHelpers } from "../shared/database"

// Secret must be defined at module level within a service
const mongoUri = secret("MONGODB_URI")

// GET /energy - List energy records with filtering and pagination
export const listEnergyRecords = api(
	{ method: "GET", path: "/energy" }, 
	async (query: FilterQuery): Promise<ListEnergyResponse> => {
		const db = await DatabaseConnection.getDb(mongoUri())
		const { page, limit, skip } = DatabaseHelpers.calculatePagination(query.page, query.limit)
		
		// Build filter query
		const filter: any = {}
		if (query.userId) {
			filter.userId = query.userId
		}
		if (query.scope) {
			filter.scopeCategory = query.scope
		}
		
		// Add date range filter
		Object.assign(filter, DatabaseHelpers.buildDateRangeFilter(query.startDate, query.endDate))
		
		const projection = DatabaseHelpers.createProjection([
			'_id', 'userId', 'sourceType', 'consumption', 'unit', 'period', 
			'scopeCategory', 'emissionFactor', 'totalEmissions', 'createdAt'
		])
		
		const [energyDocs, total] = await Promise.all([
			db.collection("energy").find(filter, { projection }).skip(skip).limit(limit).toArray(),
			db.collection("energy").countDocuments(filter)
		])
		
		const energyRecords: Energy[] = energyDocs.map(doc => 
			DatabaseHelpers.convertMongoDoc<Energy>(doc, [
				'_id', 'userId', 'sourceType', 'consumption', 'unit', 'period', 
				'scopeCategory', 'emissionFactor', 'totalEmissions', 'createdAt'
			])
		)
		
		return { energyRecords, total, page, limit }
	}
)

interface CreateEnergyRequest {
	userId: string
	sourceType: EnergySourceType
	consumption: number
	unit: EnergyUnit
	period: TimePeriod
	scopeCategory: EmissionScope
	emissionFactor?: number
}

interface CreateEnergyResponse {
	energy: Energy
	success: boolean
}

// POST /energy - Create a new energy record
export const createEnergyRecord = api(
	{ method: "POST", path: "/energy" },
	async (req: CreateEnergyRequest): Promise<CreateEnergyResponse> => {
		const db = await DatabaseConnection.getDb(mongoUri())
		
		// Calculate total emissions if emission factor is provided
		const totalEmissions = req.emissionFactor ? req.consumption * req.emissionFactor : undefined
		
		const energyRecord = {
			...req,
			totalEmissions,
			createdAt: new Date(),
			updatedAt: new Date()
		}
		
		const result = await db.collection("energy").insertOne(energyRecord)
		
		const createdEnergy = DatabaseHelpers.convertMongoDoc<Energy>(
			{ ...energyRecord, _id: result.insertedId },
			['_id', 'userId', 'sourceType', 'consumption', 'unit', 'period', 'scopeCategory', 'emissionFactor', 'totalEmissions', 'createdAt', 'updatedAt']
		)
		
		return {
			energy: createdEnergy,
			success: true
		}
	}
)

interface EnergyAnalyticsRequest {
	userId?: string
	startDate?: string
	endDate?: string
	groupBy?: 'sourceType' | 'scopeCategory' | 'period'
}

interface EnergyAnalyticsResponse {
	totalConsumption: number
	totalEmissions: number
	breakdownByCategory: Array<{
		category: string
		consumption: number
		emissions: number
		percentage: number
	}>
	averageDaily: number
}

// GET /energy/analytics - Get energy consumption analytics
export const getEnergyAnalytics = api(
	{ method: "GET", path: "/energy/analytics" },
	async (query: EnergyAnalyticsRequest): Promise<EnergyAnalyticsResponse> => {
		const db = await DatabaseConnection.getDb(mongoUri())
		
		// Build match stage for aggregation
		const matchStage: any = {}
		if (query.userId) {
			matchStage.userId = query.userId
		}
		
		if (query.startDate || query.endDate) {
			matchStage.createdAt = {}
			if (query.startDate) {
				matchStage.createdAt.$gte = new Date(query.startDate)
			}
			if (query.endDate) {
				matchStage.createdAt.$lte = new Date(query.endDate)
			}
		}
		
		const groupBy = query.groupBy || 'sourceType'
		
		const pipeline = [
			{ $match: matchStage },
			{
				$group: {
					_id: `$${groupBy}`,
					totalConsumption: { $sum: '$consumption' },
					totalEmissions: { $sum: '$totalEmissions' },
					count: { $sum: 1 }
				}
			}
		]
		
		const [aggregationResults, overallStats] = await Promise.all([
			db.collection("energy").aggregate(pipeline).toArray(),
			db.collection("energy").aggregate([
				{ $match: matchStage },
				{
					$group: {
						_id: null,
						totalConsumption: { $sum: '$consumption' },
						totalEmissions: { $sum: '$totalEmissions' },
						count: { $sum: 1 }
					}
				}
			]).toArray()
		])
		
		const totalConsumption = overallStats[0]?.totalConsumption || 0
		const totalEmissions = overallStats[0]?.totalEmissions || 0
		
		const breakdownByCategory = aggregationResults.map(item => ({
			category: item._id || 'Unknown',
			consumption: item.totalConsumption,
			emissions: item.totalEmissions || 0,
			percentage: totalConsumption > 0 ? (item.totalConsumption / totalConsumption) * 100 : 0
		}))
		
		// Calculate average daily consumption (simplified calculation)
		const daysDiff = query.startDate && query.endDate 
			? Math.max(1, Math.ceil((new Date(query.endDate).getTime() - new Date(query.startDate).getTime()) / (1000 * 60 * 60 * 24)))
			: 30 // Default to 30 days
		
		const averageDaily = totalConsumption / daysDiff
		
		return {
			totalConsumption,
			totalEmissions,
			breakdownByCategory,
			averageDaily
		}
	}
)