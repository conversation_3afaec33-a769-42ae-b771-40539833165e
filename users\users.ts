import { api } from "encore.dev/api"
import { secret } from "encore.dev/config"
import { User, ListUsersResponse, PaginationQuery } from "../shared/types"
import { DatabaseConnection, DatabaseHelpers } from "../shared/database"

// Secret must be defined at module level within a service
const mongoUri = secret("MONGODB_URI")

// GET /users - List all users with optional pagination
export const listUsers = api(
	{ method: "GET", path: "/users" }, 
	async (query: PaginationQuery): Promise<ListUsersResponse> => {
		const db = await DatabaseConnection.getDb(mongoUri())
		const { page, limit, skip } = DatabaseHelpers.calculatePagination(query.page, query.limit)
		
		// Only fetch required fields using projection
		const projection = DatabaseHelpers.createProjection(['_id', 'email', 'CompanyName'])
		
		const [userDocs, total] = await Promise.all([
			db.collection("user").find({}, { projection }).skip(skip).limit(limit).toArray(),
			db.collection("user").countDocuments({})
		])
		
		// Convert MongoDB documents to plain objects
		const users: User[] = userDocs.map(doc => 
			DatabaseHelpers.convertMongoDoc<User>(doc, ['_id', 'email', 'CompanyName'])
		)
		
		return { users, total, page, limit }
	}
)
