import { MongoClient, Db, Collection, Document } from "mongodb"

export class DatabaseConnection {
	private static client: MongoClient | null = null
	private static database: Db | null = null

	static async getDb(mongoUri: string, dbName: string = "development"): Promise<Db> {
		if (!this.database) {
			try {
				this.client = new MongoClient(mongoUri)
				await this.client.connect()
				this.database = this.client.db(dbName)
				console.log("Connected to MongoDB")
			} catch (error) {
				console.error("Failed to connect to MongoDB:", error)
				throw new Error("Database connection failed")
			}
		}
		return this.database
	}

	static async disconnect(): Promise<void> {
		if (this.client) {
			await this.client.close()
			this.client = null
			this.database = null
			console.log("Disconnected from MongoDB")
		}
	}

	static getCollection<T extends Document = Document>(collectionName: string): Collection<T> {
		if (!this.database) {
			throw new Error("Database not connected. Call getDb() first.")
		}
		return this.database.collection<T>(collectionName)
	}
}

// Helper functions for common database operations
export class DatabaseHelpers {
	static convertMongoDoc<T>(doc: any, fields: (keyof T)[]): T {
		const converted: any = {}
		
		fields.forEach(field => {

			if (field === '_id' && doc._id) {
				converted._id = doc._id.toString()
			} else if (doc[field] !== undefined) {
				converted[field] = doc[field]
			}
		})
		
		return converted as T
	}

	static createProjection(fields: string[]): Record<string, 1> {
		const projection: Record<string, 1> = {}
		fields.forEach(field => {
			projection[field] = 1
		})
		return projection
	}

	static buildDateRangeFilter(startDate?: string, endDate?: string): Record<string, any> {
		const filter: Record<string, any> = {}
		
		if (startDate || endDate) {
			filter.createdAt = {}
			if (startDate) {
				filter.createdAt.$gte = new Date(startDate)
			}
			if (endDate) {
				filter.createdAt.$lte = new Date(endDate)
			}
		}
		
		return filter
	}

	static calculatePagination(page?: number, limit?: number) {
		const pageNum = Math.max(1, page || 1)
		const limitNum = Math.min(100, Math.max(1, limit || 10)) // Max 100 items per page
		const skip = (pageNum - 1) * limitNum
		
		return { page: pageNum, limit: limitNum, skip }
	}
}