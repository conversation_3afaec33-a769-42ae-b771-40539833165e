// Main export file for all transaction-related types and utilities

// Core transaction types
export * from './index'

// Emission calculation types
export * from './emissions'

// Validation and utility functions
export * from './utils'

// Re-export main interfaces for easy access
export type {
	Transaction,
	TransactionLine,
	MoneyAmount,
	Analysis,
	SupplierFactorByYear,
	
	// Scope 1 rows
	Scope1CombustionRow,
	Scope1VolumeRow,
	Scope1VehicleRow,
	Scope1FugitiveRow,
	Scope1OtherRow,
	
	// Scope 2 rows
	Scope2ElectricityRow,
	Scope2ElectricTransportRow,
	Scope2HeatRow,
	Scope2OtherEmissionRow,
	
	// Request/Response interfaces
	CreateTransactionRequest,
	UpdateTransactionRequest,
	TransactionFilterQuery,
	ListTransactionsResponse,
	TransactionAnalyticsResponse
} from './index'

// Re-export emission types
export type {
	EmissionCalculationData,
	Scope1EmissionData,
	Scope2EmissionData,
	Scope3EmissionData,
	TotalEmissionSummary,
	EmissionFactor,
	EmissionCalculationRequest,
	EmissionCalculationResponse
} from './emissions'

// Re-export utility types
export type {
	ValidationResult,
	EmissionSummary,
	EnergyConsumptionSummary,
	TransactionDisplayData
} from './utils'

// Re-export enums
export {
	TransactionType,
	TransactionStatus,
	TransactionFlag
} from './index'

export {
	Scope3CalculationMethod,
	DataQualityRating,
	VerificationStatus
} from './emissions'

// Re-export utility classes
export {
	TransactionValidation,
	TransactionCalculations,
	TransactionHelpers
} from './utils'