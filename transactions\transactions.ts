import { api } from "encore.dev/api"
import { secret } from "encore.dev/config"
import { Transaction, ListTransactionsResponse, TransactionValidation } from "../shared/types/transaction/main"
import { DatabaseConnection, DatabaseHelpers } from "../shared/database"
import { ObjectId } from "mongodb"

// Secret must be defined at module level within a service
const mongoUri = secret("MONGODB_URI")

// Query interface for transaction filtering
interface TransactionQuery {
	RegistrationNumber: string
	Scope?: number
	periodYear: string
}

// GET /transactions - List transactions by RegistrationNumber and optional Scope
export const listTransactions = api(
	{ method: "GET", path: "/transactions" }, 
	async (query: TransactionQuery): Promise<ListTransactionsResponse> => {
		const db = await DatabaseConnection.getDb(mongoUri())
		//const { page, limit, skip } = DatabaseHelpers.calculatePagination(query.page, query.limit)
		
		// Build filter query based on RegistrationNumber and optional Scope
		const filter: any = {
			RegistrationNumber: query.RegistrationNumber,
            PeriodYear: query.periodYear
		}
		
		// Add optional Scope filter
		if (query.Scope !== undefined) {
			filter.Scope = query.Scope
		}
		
		// Project all relevant transaction fields
		const projection = DatabaseHelpers.createProjection([
			'_id', 'TransactionID', 'AccountID', 'AccountDescription', 'Amount', 
			'TransactionDate', 'RegistrationNumber', 'SupplierID', 'SupplierName',
			'Description', 'DescriptionDetails', 'Type', 'Status', 'Scope',
			'Scope_1', 'Scope_2', 'Scope_3', 'Scope_3_Category', 'kwh', 'liter',
			'renewable_energy', 'non_renewable_energy', 'Lines', 'Period', 'PeriodYear',
			'mobileCombustion', 'stationaryCombustion', 'supplierFactor'
		])
		
		const [transactionDocs] = await Promise.all([
			db.collection("transaction").find(filter, { projection }).toArray(),

		])
		
		// Convert MongoDB documents to Transaction objects
		const transactions: Transaction[] = transactionDocs.map(doc => 
			DatabaseHelpers.convertMongoDoc<Transaction>(doc, [
				'_id', 'TransactionID', 'AccountID', 'AccountDescription', 'Amount', 
				'TransactionDate', 'RegistrationNumber', 'SupplierID', 'SupplierName',
				'Description', 'DescriptionDetails', 'Type', 'Status', 'Scope',
				'Scope_1', 'Scope_2', 'Scope_3', 'Scope_3_Category', 'kwh', 'liter',
				'renewable_energy', 'non_renewable_energy', 'Lines', 'Period', 'PeriodYear',
				'mobileCombustion', 'stationaryCombustion', 'supplierFactor'
			])
		)
		
		return { 
			transactions, 
		}
	}
)

interface CreateTransactionRequest {
	RegistrationNumber: string
	AccountID?: string
	Amount?: number
	TransactionDate?: string
	SupplierID?: string
	SupplierName?: string
	Description?: string[]
	Type?: string
	Scope?: number
}

interface CreateTransactionResponse {
	transaction: Transaction
	success: boolean
}

// POST /transactions - Create a new transaction
export const createTransaction = api(
	{ method: "POST", path: "/transactions" },
	async (req: CreateTransactionRequest): Promise<CreateTransactionResponse> => {
		const db = await DatabaseConnection.getDb(mongoUri())
		
		// Validate the transaction data
		const validation = TransactionValidation.validateTransaction(req)
		if (!validation.isValid) {
			throw new Error(`Validation failed: ${validation.errors.join(', ')}`)
		}
		
		const transaction = {
			...req,
			TransactionID: `TXN_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`.toUpperCase(),
			Status: 0, // Draft status
			Flag: 0
		}
		
		const result = await db.collection("transactions").insertOne(transaction)
		
		const createdTransaction = DatabaseHelpers.convertMongoDoc<Transaction>(
			{ ...transaction, _id: result.insertedId },
			['_id', 'TransactionID', 'RegistrationNumber', 'AccountID', 'Amount', 'TransactionDate', 'SupplierID', 'SupplierName', 'Description', 'Type', 'Status', 'Flag', 'Scope']
		)
		
		return {
			transaction: createdTransaction,
			success: true
		}
	}
)

interface UpdateTransactionRequest {
	_id: string
	TransactionID?: string
	NaceCode?: string
	Description?: string[]
	Scope_1?: number
	Scope_2?: number
	Scope_3?: number
	marketBased?: number
	Notes?: string
	Scope_3_Category?: number
	Status?: number
	supplierFactor?: number | object
	SupplierName?: string
	SupplierID?: string
}

interface UpdateTransactionResponse {
	status: string
	TransactionID?: string
	Scope_1?: number
	Scope_2?: number
	Scope_3?: number
}

// PUT /transactions - Update a transaction by _id
export const updateTransaction = api(
	{ method: "PUT", path: "/transactions" },
	async (input: UpdateTransactionRequest): Promise<UpdateTransactionResponse> => {
		try {
			const {
				NaceCode,
				Description,
				Scope_1,
				Scope_2,
				Scope_3,
				marketBased,
				Notes,
				Scope_3_Category,
				Status,
				supplierFactor,
				SupplierName,
				SupplierID,
			} = input

			const db = await DatabaseConnection.getDb(mongoUri())
			
			// Convert string _id to ObjectId
			const transactionId = new ObjectId(input._id)
			
			// Build payload with the specified fields
			let payload: any = { 
				NaceCode, 
				Description, 
				Scope_1, 
				Scope_2, 
				Scope_3, 
				Notes, 
				Scope_3_Category, 
				Status, 
				marketBased 
			}
			
			// Add supplier information if SupplierID is provided
			if (SupplierID) {
				payload.SupplierID = SupplierID
				payload.SupplierName = SupplierName
				payload.supplierFactor = supplierFactor
			}

			// Remove undefined values from payload
			payload = Object.fromEntries(
				Object.entries(payload).filter(([_, value]) => value !== undefined)
			)

			const result = await db.collection("transaction").updateOne(
				{ _id: transactionId },
				{ $set: payload }
			)

			if (result.matchedCount === 0) {
				return { status: "error" }
			}

			return { 
				status: "done", 
				TransactionID: input.TransactionID, 
				Scope_1, 
				Scope_2, 
				Scope_3 
			}
		} catch (err) {
			console.log(err)
			return { status: "error" }
		}
	}
)

interface UpdateTransactionScopeRequest {
	transactionId: string
	transaction: Transaction
}

interface UpdateTransactionScopeResponse {
	status: string
	transaction?: Transaction
	error?: any
}

// PUT /transactions/scope - Update transaction scope data
export const updateTransactionScope = api(
	{ method: "PUT", path: "/transactions/scope" },
	async (input: UpdateTransactionScopeRequest): Promise<UpdateTransactionScopeResponse> => {
		try {
			const db = await DatabaseConnection.getDb(mongoUri())
			
			// Convert string transactionId to ObjectId
			const transactionId = new ObjectId(input.transactionId)
			
			const result = await db.collection("transaction").updateOne(
				{ _id: transactionId },
				{ $set: input.transaction }
			)

			if (result.matchedCount === 0) {
				return { status: "error", error: "Transaction not found" }
			}

			return { status: "done", transaction: input.transaction }
		} catch (err) {
			return { status: "error", error: err }
		}
	}
)