// Common types used across multiple services

// Re-export comprehensive transaction types
export * from './transaction/main'

// Legacy simple transaction interface (for backward compatibility)
export interface SimpleTransaction {
	_id?: string
	userId: string
	type: SimpleTransactionType
	amount: number
	currency: string
	description?: string
	category?: string
	energyUsed?: number
	scopeCategory?: ScopeCategory
	createdAt?: Date
	updatedAt?: Date
}

export enum SimpleTransactionType {
	PURCHASE = "purchase",
	CONSUMPTION = "consumption",
	OFFSET = "offset"
}

export interface User {
	_id?: string
	name: string
	email: string
	CompanyName: string
	createdAt?: Date
	updatedAt?: Date
}

// Removed old Transaction interface - now using comprehensive Transaction from transaction/main.ts

export interface Energy {
	_id?: string
	userId: string
	sourceType: EnergySourceType
	consumption: number
	unit: EnergyUnit
	period: TimePeriod
	scopeCategory: ScopeCategory
	emissionFactor?: number
	totalEmissions?: number
	createdAt?: Date
	updatedAt?: Date
}

export interface ScopeCategory {
	_id?: string
	scope: EmissionScope
	category: string
	description?: string
	emissionFactor: number
	unit: string
	isActive: boolean
	createdAt?: Date
	updatedAt?: Date
}

// Enums and types
// Note: TransactionType is now available from './transaction/main'

export enum EnergySourceType {
	ELECTRICITY = "electricity",
	GAS = "gas",
	FUEL = "fuel",
	RENEWABLE = "renewable"
}

export enum EnergyUnit {
	KWH = "kWh",
	MWH = "MWh",
	CUBIC_METER = "m³",
	LITER = "L",
	GALLON = "gal"
}

export enum EmissionScope {
	SCOPE_1 = "scope1", // Direct emissions
	SCOPE_2 = "scope2", // Indirect emissions from electricity
	SCOPE_3 = "scope3"  // Other indirect emissions
}

export interface TimePeriod {
	startDate: Date
	endDate: Date
	type: PeriodType
}

export enum PeriodType {
	DAILY = "daily",
	WEEKLY = "weekly",
	MONTHLY = "monthly",
	QUARTERLY = "quarterly",
	YEARLY = "yearly"
}

// Response interfaces
export interface ListUsersResponse {
	users: User[]
	total: number
	page?: number
	limit?: number
}

// Note: ListTransactionsResponse is now available from './transaction/main'
// Keeping legacy response for backward compatibility
export interface SimpleLegacyListTransactionsResponse {
	transactions: SimpleTransaction[]
	total: number
	page?: number
	limit?: number
}

export interface ListEnergyResponse {
	energyRecords: Energy[]
	total: number
	page?: number
	limit?: number
}

export interface ListScopeCategoriesResponse {
	scopeCategories: ScopeCategory[]
	total: number
	page?: number
	limit?: number
}

// Common query interfaces
export interface PaginationQuery {
	page?: number
	limit?: number
}

export interface DateRangeQuery {
	startDate?: string
	endDate?: string
}

export interface FilterQuery extends PaginationQuery, DateRangeQuery {
	userId?: string
	category?: string
	scope?: EmissionScope
}