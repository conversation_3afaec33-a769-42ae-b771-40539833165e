// Transaction-specific types based on the complete transaction schema

import { ObjectId } from "mongodb"

// Base transaction interface
export interface Transaction {
	_id?: string
	AccountDescription?: string
	AccountID?: string
	AccountIDInRange?: boolean
	Amount?: number
	Auto?: boolean
	Description?: string[]
	DescriptionDetails?: string
	Flag?: number
	"Goods-type"?: string
	ImportID?: string
	IsSplittedTransaction?: boolean
	Lines?: TransactionLine[]
	NaceCode?: string
	Notes?: string
	Period?: string
	PeriodYear?: string
	"Production-related"?: boolean
	ReferenceNumber?: string
	RegistrationNumber?: string
	Relation?: string
	Scope: number
	Scope_1: number
	Scope_2: number
	Scope_3: number
	Scope_3_Category: number
	Status: number
	SupplierID?: string
	SupplierName?: string
	SystemID?: string
	TransactionDate?: string
	TransactionID?: string
	Type?: string
	kwh?: number
	liter?: number
	mobileCombustion?: number
	non_renewable_energy?: number
	nuclear?: number
	renewable_energy?: number
	stationaryCombustion?: number
	supplierFactor?: number | SupplierFactorByYear
	scope1CombustionRows?: Scope1CombustionRow[]
	scope1VolumeRows?: Scope1VolumeRow[]
	scope1VehicleRows?: Scope1VehicleRow[]
	scope1FugitiveRows?: Scope1FugitiveRow[]
	scope1OtherRows?: Scope1OtherRow[]
	scope2ElectricityRows?: Scope2ElectricityRow[]
	scope2ElectricTransportRows?: Scope2ElectricTransportRow[]
	scope2HeatRows?: Scope2HeatRow[]
	scope2OtherEmissionRows?: Scope2OtherEmissionRow[]
}

// Transaction line item
export interface TransactionLine {
	AccountDescription?: string
	AccountID?: string
	Analysis?: Analysis[]
	CreditAmount?: MoneyAmount
	DebitAmount?: MoneyAmount
	Description?: string
	LineAccountIdInRange?: boolean
	NaceCode?: string
	RecordID?: string
	ReferenceNumber?: string
	Status?: string
	SupplierID?: string
}

// Analysis data for transaction lines
export interface Analysis {
	AnalysisID?: string
	AnalysisType?: string
}

// Money amount with currency information
export interface MoneyAmount {
	Amount?: string
	CurrencyAmount?: string
	CurrencyCode?: string
	ExchangeRate?: string
}

// Supplier factor can be a number or object with yearly data
export interface SupplierFactorByYear {
	[year: string]: number // Year as key (e.g., "2024": 0.5)
}

// Scope 1 emission calculation rows
export interface Scope1CombustionRow {
	metric?: string
	factor?: number
	unit?: number
	unitType?: string
	scope1?: number
	scope2?: number
	scope3?: number
	stationary?: number
	kwh?: number
}

export interface Scope1VolumeRow {
	matric?: string // Note: seems to be a typo in schema, might be "metric"
	factor?: number
	unit?: number
	unitType?: string
	scope1?: number
	scope2?: number
	scope3?: number
	kwh?: number
	mobileCombustion?: number
}

export interface Scope1VehicleRow {
	vehicleType?: string
	fuelType?: string
	distance?: number
	economy?: string
	scope1?: number
	scope2?: number
	scope3?: number
	distanceUnit?: string
	economyUnit?: string
	kwh?: number
	mobileCombustion?: number
	renewable?: number
	non_renewable_energy?: number
}

export interface Scope1FugitiveRow {
	metric?: string
	factor?: number
	unit?: number
	unitType?: string
	scope1?: number
	scope2?: number
	scope3?: number
}

export interface Scope1OtherRow {
	metric?: string
	factor?: number
	unit?: number
	scope1?: number
	scope2?: number
	scope3?: number
}

// Scope 2 emission calculation rows
export interface Scope2ElectricityRow {
	metric?: string
	factor?: number
	unit?: number
	scope1?: number
	scope2?: number
	scope3?: number
	kwh?: number
	locationBased?: number
	consumptionBased?: number
	marketBased?: number
	nuclear?: number
	renewable_energy?: number
	non_renewable_energy?: number
}

export interface Scope2ElectricTransportRow {
	type?: string
	factor?: number
	distance?: number
	scope1?: number
	scope2?: number
	scope3?: number
	kwh?: number
	locationBased?: number
	consumptionBased?: number
	marketBased?: number
	renewable_energy?: number
	non_renewable_energy?: number
}

export interface Scope2HeatRow {
	metric?: string
	factor?: number
	unit?: number
	scope2?: number
}

export interface Scope2OtherEmissionRow {
	metric?: string
	factor?: number
	unit?: number
	scope2?: number
}

// Enums for transaction properties
export enum TransactionType {
	PURCHASE = "purchase",
	CONSUMPTION = "consumption",
	OFFSET = "offset",
	ENERGY = "energy",
	TRANSPORT = "transport",
	WASTE = "waste"
}

export enum TransactionStatus {
	DRAFT = 0,
	ACTIVE = 1,
	PROCESSED = 2,
	CANCELLED = 3
}

export enum TransactionFlag {
	NONE = 0,
	IMPORTANT = 1,
	REQUIRES_REVIEW = 2,
	AUTO_CALCULATED = 3
}

// Response interfaces for transaction operations
export interface CreateTransactionRequest {
	AccountID?: string
	Amount?: number
	Description?: string[]
	SupplierID?: string
	TransactionDate?: string
	Type?: string
	Lines?: CreateTransactionLineRequest[]
}

export interface CreateTransactionLineRequest {
	AccountID?: string
	Description?: string
	CreditAmount?: MoneyAmount
	DebitAmount?: MoneyAmount
}

export interface UpdateTransactionRequest {
	TransactionID: string
	AccountDescription?: string
	Amount?: number
	Description?: string[]
	Status?: TransactionStatus
	Notes?: string
}

export interface TransactionFilterQuery {
	page?: number
	limit?: number
	SupplierID?: string
	AccountID?: string
	TransactionDate?: string
	startDate?: string
	endDate?: string
	Type?: string
	Status?: TransactionStatus
	Scope?: number
	hasScope1Data?: boolean
	hasScope2Data?: boolean
	hasScope3Data?: boolean
}

export interface ListTransactionsResponse {
	transactions: Transaction[]
	page?: number
	limit?: number
	totalScope1Emissions?: number
	totalScope2Emissions?: number
	totalScope3Emissions?: number
}

export interface TransactionAnalyticsResponse {
	totalTransactions: number
	totalAmount: number
	totalEmissions: {
		scope1: number
		scope2: number
		scope3: number
		total: number
	}
	energyConsumption: {
		kwh: number
		renewable: number
		nonRenewable: number
	}
	breakdownBySupplier: Array<{
		supplierID: string
		supplierName: string
		transactionCount: number
		totalAmount: number
		totalEmissions: number
	}>
	breakdownByType: Array<{
		type: string
		count: number
		totalAmount: number
		totalEmissions: number
	}>
	breakdownByScope: Array<{
		scope: number
		emissions: number
		percentage: number
	}>
}