// Emission calculation specific types for transactions
import type {
	Scope1CombustionRow,
	Scope1VolumeRow,
	Scope1VehicleRow,
	Scope1Fug<PERSON>Row,
	Scope1OtherRow,
	Scope2ElectricityRow,
	Scope2Electric<PERSON>ransportRow,
	Scope2HeatRow,
	Scope2<PERSON>therEmissionRow
} from './index'

export interface EmissionCalculationData {
	scope1?: Scope1EmissionData
	scope2?: Scope2EmissionData  
	scope3?: Scope3EmissionData
	total?: TotalEmissionSummary
}

export interface Scope1EmissionData {
	combustionEmissions?: number
	volumeEmissions?: number
	vehicleEmissions?: number
	fugitiveEmissions?: number
	otherEmissions?: number
	totalScope1?: number
	combustionRows?: Scope1CombustionRow[]
	volumeRows?: Scope1VolumeRow[]
	vehicleRows?: Scope1VehicleRow[]
	fugitiveRows?: Scope1FugitiveRow[]
	otherRows?: Scope1OtherRow[]
}

export interface Scope2EmissionData {
	electricityEmissions?: number
	electricTransportEmissions?: number
	heatEmissions?: number
	otherEmissions?: number
	totalScope2?: number
	locationBased?: number
	marketBased?: number
	consumptionBased?: number
	electricityRows?: Scope2ElectricityRow[]
	transportRows?: Scope2ElectricTransportRow[]
	heatRows?: Scope2HeatRow[]
	otherRows?: Scope2OtherEmissionRow[]
}

export interface Scope3EmissionData {
	category?: number
	categoryDescription?: string
	emissions?: number
	calculationMethod?: Scope3CalculationMethod
	dataQuality?: DataQualityRating
}

export interface TotalEmissionSummary {
	scope1Total: number
	scope2Total: number
	scope3Total: number
	grandTotal: number
	carbonIntensity?: number // emissions per unit of activity
	emissionFactorUsed?: string
	calculationDate?: Date
	verificationStatus?: VerificationStatus
}

// Re-export types from main transaction file for convenience
export type {
	Scope1CombustionRow,
	Scope1VolumeRow,
	Scope1VehicleRow,
	Scope1FugitiveRow,
	Scope1OtherRow,
	Scope2ElectricityRow,
	Scope2ElectricTransportRow,
	Scope2HeatRow,
	Scope2OtherEmissionRow
} from './index'

// Enums for emission calculations
export enum Scope3CalculationMethod {
	SPEND_BASED = "spend_based",
	ACTIVITY_BASED = "activity_based",
	SUPPLIER_SPECIFIC = "supplier_specific",
	HYBRID = "hybrid"
}

export enum DataQualityRating {
	HIGH = "high",
	MEDIUM = "medium", 
	LOW = "low",
	ESTIMATED = "estimated"
}

export enum VerificationStatus {
	NOT_VERIFIED = "not_verified",
	SELF_VERIFIED = "self_verified",
	THIRD_PARTY_VERIFIED = "third_party_verified",
	CERTIFIED = "certified"
}

// Emission factor interfaces
export interface EmissionFactor {
	id?: string
	name: string
	value: number
	unit: string
	source: string
	region?: string
	year?: string
	scope: number
	category?: string
	subcategory?: string
	dataQuality: DataQualityRating
	lastUpdated?: Date
}

export interface EmissionCalculationRequest {
	transactionId: string
	calculationMethod: Scope3CalculationMethod
	emissionFactors?: EmissionFactor[]
	customFactors?: Record<string, number>
	includeUncertainty?: boolean
}

export interface EmissionCalculationResponse {
	transactionId: string
	calculatedEmissions: EmissionCalculationData
	calculationMetadata: {
		method: Scope3CalculationMethod
		factorsUsed: EmissionFactor[]
		calculationDate: Date
		dataQuality: DataQualityRating
		uncertaintyRange?: {
			lower: number
			upper: number
			confidence: number
		}
	}
	warnings?: string[]
	errors?: string[]
}