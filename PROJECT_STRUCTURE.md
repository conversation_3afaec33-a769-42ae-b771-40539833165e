# Encore.dev Application Structure

This is a well-organized Encore.dev application with multiple services for managing users, transactions, energy consumption, and emission scope categories.

## 📁 Project Structure

```
testapp/
├── shared/                      # Shared utilities and types
│   ├── types/
│   │   └── index.ts            # Common interfaces and types
│   ├── database/
│   │   └── index.ts            # Database connection and helpers
│   └── utils/
│       └── index.ts            # Common utility functions
│
├── users/                      # User management service
│   ├── encore.service.ts       # Service definition
│   └── users.ts               # User API endpoints
│
├── transactions/               # Transaction management service
│   ├── encore.service.ts       # Service definition
│   └── transactions.ts        # Transaction API endpoints
│
├── energy/                     # Energy consumption service
│   ├── encore.service.ts       # Service definition
│   └── energy.ts              # Energy API endpoints
│
├── scope-categories/           # Emission scope categories service
│   ├── encore.service.ts       # Service definition
│   └── scope-categories.ts    # Scope categories API endpoints
│
├── hello/                      # Example service (can be removed)
│   ├── encore.service.ts
│   ├── hello.ts
│   └── hello.test.ts
│
├── encore.app                  # Application configuration
├── package.json               # Dependencies and scripts
├── tsconfig.json              # TypeScript configuration
└── README.md                  # This file
```

## 🚀 Services Overview

### Users Service (`/users`)
- **GET /users** - List users with pagination
- Manages user accounts and profiles
- Includes company information

### Transactions Service (`/transactions`)
- **GET /transactions** - List transactions with filtering
- **POST /transactions** - Create new transaction
- **GET /transactions/:id** - Get specific transaction
- Handles financial transactions related to emissions

### Energy Service (`/energy`)
- **GET /energy** - List energy consumption records
- **POST /energy** - Create new energy record
- **GET /energy/analytics** - Get consumption analytics
- Tracks energy usage across different sources

### Scope Categories Service (`/scope-categories`)
- **GET /scope-categories** - List emission scope categories
- **POST /scope-categories** - Create new category
- **PUT /scope-categories/:id** - Update category
- **GET /scope-categories/:id** - Get specific category
- **GET /scope-categories/by-scope/:scope** - Get categories by scope
- Manages emission scopes (Scope 1, 2, 3)

## 🛠 Shared Components

### Types (`shared/types/`)
Common TypeScript interfaces and enums:
- **User** - User account structure
- **Transaction** - Financial transaction data
- **Energy** - Energy consumption records
- **ScopeCategory** - Emission category definitions
- **Enums** - TransactionType, EnergySourceType, EmissionScope, etc.

### Database (`shared/database/`)
Centralized database utilities:
- **DatabaseConnection** - MongoDB connection management
- **DatabaseHelpers** - Common database operations
- Document conversion and pagination utilities

### Utils (`shared/utils/`)
Common utility functions:
- **ValidationUtils** - Input validation
- **DateUtils** - Date manipulation
- **NumberUtils** - Number formatting and calculations
- **EmissionCalculator** - Carbon emission calculations
- **ResponseUtils** - API response formatting

## 🔧 Getting Started

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Set up MongoDB secret:**
   ```bash
   encore secret set --type dev MONGODB_URI "your-mongodb-connection-string"
   ```

3. **Run the application:**
   ```bash
   encore run
   ```

4. **Access the API:**
   - API: http://127.0.0.1:4000
   - Dashboard: http://127.0.0.1:9400

## 📡 API Endpoints

### Users
- `GET /users?page=1&limit=10` - List users
- `GET /users?userId=123` - Filter by user

### Transactions  
- `GET /transactions?page=1&limit=10&userId=123&category=energy` - List transactions
- `POST /transactions` - Create transaction
- `GET /transactions/:id` - Get transaction

### Energy
- `GET /energy?page=1&limit=10&scope=scope1` - List energy records
- `POST /energy` - Create energy record
- `GET /energy/analytics?userId=123&startDate=2024-01-01&endDate=2024-12-31` - Analytics

### Scope Categories
- `GET /scope-categories?scope=scope1&isActive=true` - List categories
- `POST /scope-categories` - Create category
- `PUT /scope-categories/:id` - Update category
- `GET /scope-categories/by-scope/scope1` - Get by emission scope

## 🎯 Features

- **Modular Architecture** - Each service is independent and focused
- **Shared Components** - Reusable types, database utilities, and helpers
- **Type Safety** - Full TypeScript support with proper interfaces
- **Pagination** - Built-in pagination for list endpoints
- **Filtering** - Advanced filtering capabilities
- **Analytics** - Energy consumption analytics and calculations
- **Validation** - Input validation and sanitization
- **Error Handling** - Comprehensive error messages and handling

## 📊 Data Models

The application handles several key data models:

1. **Users** - Account management with company information
2. **Transactions** - Financial records linked to energy consumption
3. **Energy Records** - Consumption data with emission calculations
4. **Scope Categories** - Emission factor definitions for different categories

## 🔐 Environment Configuration

Required secrets:
- `MONGODB_URI` - MongoDB connection string

## 🧪 Development

To add a new service:

1. Create a new directory with the service name
2. Add `encore.service.ts` file with service definition
3. Add your API endpoints in a TypeScript file
4. Import shared types and utilities as needed
5. Follow the existing patterns for consistency

## 📚 Additional Resources

- [Encore.dev Documentation](https://encore.dev/docs)
- [MongoDB Node.js Driver](https://docs.mongodb.com/drivers/node/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)