import accountDataJson from "../../shared/data/accountData.json"

interface AccountItem {
	description: string
	"description-details": string
	"emission-factor": string | number
	"function": string
	"industry-code": string | number
	notes: string
	relation: string
	scope: string | number
	"scope-3-category": string | number
	status: string | number
	type: string
}

const accountData = accountDataJson as Record<string, AccountItem>

interface ClassifyTransactionArgs {
	AccountID: string
	StandardAccountID: string
}

export default function ({ AccountID, StandardAccountID = "" }: ClassifyTransactionArgs) {
	try {
		// get the account data from value AccountID and not the AccountData passed from insertTransaction
		// Variable to store an AccountID
		let finalAccountID = ""

		// Check if the AccountID on the transaction is in the AccountData object
		// If found - all is good.
		if (AccountID in accountData) {
			finalAccountID = AccountID

			// If the AccountID is not there -> run getStandardAccountID to get the StandardAccoundID that might be stored in Accounts
		} else {
			finalAccountID = StandardAccountID

			// Then resort what the finalAccountID should be
			// finalAccountID = getAccountId(transaction.AccountID, StandardAccountID)
		}
		if (!finalAccountID) {
			// This might be the best place to handle no AccountID error.
			return {}
		}

		// Get the data from the accountData object
		// If the final accountID is not in accountData
		// then the substring of the 2 first chars should be used and should be found
		if (!(finalAccountID in accountData)) {
			finalAccountID = finalAccountID.substring(0, 2)
		}
		const data = accountData[finalAccountID]
		const dataToReturn = {
			// Return the finalAccountID as AccountID - Not used on the other side.
			AccountID: finalAccountID,
			Status: data["status"],
			DescriptionDetails: data["description-details"],
			Scope: data["scope"],
			Scope_3_Category: data["scope-3-category"],
			Notes: data["notes"],
			Relation: data["relation"],
			Type: data["type"],
            function: ""
		}
		if ("function" in data) {
			dataToReturn["function"] = data.function
		}
		return dataToReturn
	} catch (error) {
		return { Status: -1, Scope: 0 }
	}
}
