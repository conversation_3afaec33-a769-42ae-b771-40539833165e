{"properties": {"AccountDescription": {"bsonType": "string"}, "AccountID": {"bsonType": "string"}, "AccountIDInRange": {"bsonType": "bool"}, "Amount": {"bsonType": "number"}, "Auto": {"bsonType": "bool"}, "Description": {"bsonType": "array", "items": {"bsonType": "string"}}, "DescriptionDetails": {"bsonType": "string"}, "Flag": {"bsonType": "int"}, "Goods-type": {"bsonType": "string"}, "ImportID": {"bsonType": "string"}, "IsSplittedTransaction": {"bsonType": "bool"}, "Lines": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"AccountDescription": {"bsonType": "string"}, "AccountID": {"bsonType": "string"}, "Analysis": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"AnalysisID": {"bsonType": "string"}, "AnalysisType": {"bsonType": "string"}}}}, "CreditAmount": {"bsonType": "object", "properties": {"Amount": {"bsonType": "string"}, "CurrencyAmount": {"bsonType": "string"}, "CurrencyCode": {"bsonType": "string"}, "ExchangeRate": {"bsonType": "string"}}}, "DebitAmount": {"bsonType": "object", "properties": {"Amount": {"bsonType": "string"}, "CurrencyAmount": {"bsonType": "string"}, "CurrencyCode": {"bsonType": "string"}, "ExchangeRate": {"bsonType": "string"}}}, "Description": {"bsonType": "string", "default": ""}, "LineAccountIdInRange": {"bsonType": "bool"}, "NaceCode": {"bsonType": "string"}, "RecordID": {"bsonType": "string"}, "ReferenceNumber": {"bsonType": "string"}, "Status": {"bsonType": "string"}, "SupplierID": {"bsonType": "string"}}}}, "NaceCode": {"bsonType": "string"}, "Notes": {"bsonType": "string"}, "Period": {"bsonType": "string"}, "PeriodYear": {"bsonType": "string"}, "Production-related": {"bsonType": "bool"}, "ReferenceNumber": {"bsonType": "string"}, "RegistrationNumber": {"bsonType": "string"}, "Relation": {"bsonType": "string"}, "Scope": {"bsonType": "number"}, "Scope_1": {"bsonType": "number"}, "Scope_2": {"bsonType": "number"}, "Scope_3": {"bsonType": "number"}, "Scope_3_Category": {"bsonType": "number"}, "Status": {"bsonType": "int"}, "SupplierID": {"bsonType": "string"}, "SupplierName": {"bsonType": "string"}, "SystemID": {"bsonType": "string"}, "TransactionDate": {"bsonType": "string"}, "TransactionID": {"bsonType": "string"}, "Type": {"bsonType": "string"}, "_id": {"bsonType": "objectId"}, "kwh": {"bsonType": "number"}, "liter": {"bsonType": "number"}, "mobileCombustion": {"bsonType": "number"}, "non_renewable_energy": {"bsonType": "number"}, "nuclear": {"bsonType": "number"}, "renewable_energy": {"bsonType": "number"}, "stationaryCombustion": {"bsonType": "number"}, "supplierFactor": {"oneOf": [{"minimum": {"$numberInt": "0"}, "type": "number"}, {"additionalProperties": false, "patternProperties": {"^[0-9]{4}$": {"type": "number"}}, "type": "object"}]}, "scope1CombustionRows": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"metric": {"bsonType": "string"}, "factor": {"bsonType": "number"}, "unit": {"bsonType": "number"}, "unitType": {"bsonType": "string"}, "scope1": {"bsonType": "number"}, "scope2": {"bsonType": "number"}, "scope3": {"bsonType": "number"}, "stationary": {"bsonType": "number"}, "kwh": {"bsonType": "number"}}}}, "scope1VolumeRows": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"matric": {"bsonType": "string"}, "factor": {"bsonType": "number"}, "unit": {"bsonType": "number"}, "unitType": {"bsonType": "string"}, "scope1": {"bsonType": "number"}, "scope2": {"bsonType": "number"}, "scope3": {"bsonType": "number"}, "kwh": {"bsonType": "number"}, "mobileCombustion": {"bsonType": "number"}}}}, "scope1VehicleRows": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"vehicleType": {"bsonType": "string"}, "fuelType": {"bsonType": "string"}, "distance": {"bsonType": "number"}, "economy": {"bsonType": "string"}, "scope1": {"bsonType": "number"}, "scope2": {"bsonType": "number"}, "scope3": {"bsonType": "number"}, "distanceUnit": {"bsonType": "string"}, "economyUnit": {"bsonType": "string"}, "kwh": {"bsonType": "number"}, "mobileCombustion": {"bsonType": "number"}, "renewable": {"bsonType": "number"}, "non_renewable_energy": {"bsonType": "number"}}}}, "scope1FugitiveRows": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"metric": {"bsonType": "string"}, "factor": {"bsonType": "number"}, "unit": {"bsonType": "number"}, "unitType": {"bsonType": "string"}, "scope1": {"bsonType": "number"}, "scope2": {"bsonType": "number"}, "scope3": {"bsonType": "number"}}}}, "scope1OtherRows": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"metric": {"bsonType": "string"}, "factor": {"bsonType": "number"}, "unit": {"bsonType": "number"}, "scope1": {"bsonType": "number"}, "scope2": {"bsonType": "number"}, "scope3": {"bsonType": "number"}}}}, "scope2ElectricityRows": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"metric": {"bsonType": "string"}, "factor": {"bsonType": "number"}, "unit": {"bsonType": "number"}, "scope1": {"bsonType": "number"}, "scope2": {"bsonType": "number"}, "scope3": {"bsonType": "number"}, "kwh": {"bsonType": "number"}, "locationBased": {"bsonType": "number"}, "consumptionBased": {"bsonType": "number"}, "marketBased": {"bsonType": "number"}, "nuclear": {"bsonType": "number"}, "renewable_energy": {"bsonType": "number"}, "non_renewable_energy": {"bsonType": "number"}}}}, "scope2ElectricTransportRows": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"type": {"bsonType": "string"}, "factor": {"bsonType": "double"}, "distance": {"bsonType": "double"}, "scope1": {"bsonType": "double"}, "scope2": {"bsonType": "double"}, "scope3": {"bsonType": "double"}, "kwh": {"bsonType": "double"}, "locationBased": {"bsonType": "double"}, "consumptionBased": {"bsonType": "double"}, "marketBased": {"bsonType": "double"}, "renewable_energy": {"bsonType": "double"}, "non_renewable_energy": {"bsonType": "double"}}}}, "scope2HeatRows": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"metric": {"bsonType": "string"}, "factor": {"bsonType": "number"}, "unit": {"bsonType": "number"}, "scope2": {"bsonType": "number"}}}}, "scope2OtherEmissionRows": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"metric": {"bsonType": "string"}, "factor": {"bsonType": "number"}, "unit": {"bsonType": "number"}, "scope2": {"bsonType": "number"}}}}}, "title": "transaction"}