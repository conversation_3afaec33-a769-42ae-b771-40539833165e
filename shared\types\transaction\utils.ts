// Transaction validation and utility functions

import type { 
	Transaction, 
	TransactionLine, 
	MoneyAmount,
	TransactionStatus,
	TransactionFlag 
} from './index'
import type { EmissionCalculationData } from './emissions'

export class TransactionValidation {
	static validateTransaction(transaction: Partial<Transaction>): ValidationResult {
		const errors: string[] = []
		const warnings: string[] = []

		// Required field validations
		if (!transaction.TransactionID) {
			errors.push('TransactionID is required')
		}

		if (!transaction.TransactionDate) {
			errors.push('TransactionDate is required')
		} else if (!this.isValidDate(transaction.TransactionDate)) {
			errors.push('TransactionDate must be a valid date string')
		}

		if (transaction.Amount !== undefined && transaction.Amount < 0) {
			errors.push('Amount cannot be negative')
		}

		// Line item validations
		if (transaction.Lines && transaction.Lines.length > 0) {
			transaction.Lines.forEach((line, index) => {
				const lineErrors = this.validateTransactionLine(line)
				lineErrors.forEach(error => {
					errors.push(`Line ${index + 1}: ${error}`)
				})
			})
		}

		// Business logic validations
		if (transaction.IsSplittedTransaction && (!transaction.Lines || transaction.Lines.length <= 1)) {
			warnings.push('Transaction marked as split but has only one or no line items')
		}

		if (transaction.Scope_1 || transaction.Scope_2 || transaction.Scope_3) {
			if (!transaction.kwh && !transaction.liter && !transaction.mobileCombustion) {
				warnings.push('Emission scopes are set but no energy consumption data provided')
			}
		}

		return {
			isValid: errors.length === 0,
			errors,
			warnings
		}
	}

	static validateTransactionLine(line: TransactionLine): string[] {
		const errors: string[] = []

		if (!line.AccountID) {
			errors.push('AccountID is required for transaction line')
		}

		// Validate that either credit or debit amount is provided, but not both
		const hasCreditAmount = line.CreditAmount && this.hasMoneyValue(line.CreditAmount)
		const hasDebitAmount = line.DebitAmount && this.hasMoneyValue(line.DebitAmount)

		if (!hasCreditAmount && !hasDebitAmount) {
			errors.push('Either CreditAmount or DebitAmount must be provided')
		}

		if (hasCreditAmount && hasDebitAmount) {
			errors.push('Cannot have both CreditAmount and DebitAmount in the same line')
		}

		return errors
	}

	static validateMoneyAmount(amount: MoneyAmount): string[] {
		const errors: string[] = []

		if (!amount.Amount) {
			errors.push('Amount is required')
		} else if (isNaN(parseFloat(amount.Amount))) {
			errors.push('Amount must be a valid number')
		}

		if (!amount.CurrencyCode) {
			errors.push('CurrencyCode is required')
		} else if (amount.CurrencyCode.length !== 3) {
			errors.push('CurrencyCode must be a 3-letter ISO currency code')
		}

		return errors
	}

	private static hasMoneyValue(amount: MoneyAmount): boolean {
		return !!(amount.Amount && parseFloat(amount.Amount) !== 0)
	}

	private static isValidDate(dateString: string): boolean {
		const date = new Date(dateString)
		return date instanceof Date && !isNaN(date.getTime())
	}
}

export class TransactionCalculations {
	static calculateTotalAmount(transaction: Transaction): number {
		if (transaction.Amount) {
			return transaction.Amount
		}

		if (!transaction.Lines || transaction.Lines.length === 0) {
			return 0
		}

		return transaction.Lines.reduce((total, line) => {
			const creditAmount = line.CreditAmount ? parseFloat(line.CreditAmount.Amount || '0') : 0
			const debitAmount = line.DebitAmount ? parseFloat(line.DebitAmount.Amount || '0') : 0
			return total + Math.abs(creditAmount - debitAmount)
		}, 0)
	}

	static calculateTotalEmissions(transaction: Transaction): EmissionSummary {
		return {
			scope1: transaction.Scope_1 || 0,
			scope2: transaction.Scope_2 || 0,
			scope3: transaction.Scope_3 || 0,
			total: (transaction.Scope_1 || 0) + (transaction.Scope_2 || 0) + (transaction.Scope_3 || 0)
		}
	}

	static calculateEnergyConsumption(transaction: Transaction): EnergyConsumptionSummary {
		return {
			kwh: transaction.kwh || 0,
			liter: transaction.liter || 0,
			renewable: transaction.renewable_energy || 0,
			nonRenewable: transaction.non_renewable_energy || 0,
			nuclear: transaction.nuclear || 0,
			stationaryCombustion: transaction.stationaryCombustion || 0,
			mobileCombustion: transaction.mobileCombustion || 0
		}
	}

	static calculateEmissionIntensity(transaction: Transaction): number {
		const totalEmissions = this.calculateTotalEmissions(transaction).total
		const totalAmount = this.calculateTotalAmount(transaction)
		
		if (totalAmount === 0) return 0
		return totalEmissions / totalAmount
	}
}

export class TransactionHelpers {
	static createEmptyTransaction(supplierID?: string): Partial<Transaction> {
		return {
			TransactionID: this.generateTransactionId(),
			TransactionDate: new Date().toISOString().split('T')[0],
			Status: 0, // Draft status
			Flag: 0, // No flag
			SupplierID: supplierID,
			Lines: [],
			Auto: false,
			IsSplittedTransaction: false
		}
	}

	static generateTransactionId(): string {
		const timestamp = Date.now()
		const random = Math.random().toString(36).substr(2, 5)
		return `TXN_${timestamp}_${random}`.toUpperCase()
	}

	static formatTransactionForDisplay(transaction: Transaction): TransactionDisplayData {
		const emissions = TransactionCalculations.calculateTotalEmissions(transaction)
		const energy = TransactionCalculations.calculateEnergyConsumption(transaction)
		
		return {
			id: transaction.TransactionID || '',
			date: transaction.TransactionDate || '',
			supplier: transaction.SupplierName || 'Unknown Supplier',
			amount: TransactionCalculations.calculateTotalAmount(transaction),
			description: transaction.DescriptionDetails || (transaction.Description && transaction.Description[0]) || '',
			status: this.getStatusText(transaction.Status || 0),
			totalEmissions: emissions.total,
			energyConsumption: energy.kwh,
			scope1: emissions.scope1,
			scope2: emissions.scope2,
			scope3: emissions.scope3
		}
	}

	private static getStatusText(status: number): string {
		const statusMap: Record<number, string> = {
			0: 'Draft',
			1: 'Active',
			2: 'Processed',
			3: 'Cancelled'
		}
		return statusMap[status] || 'Unknown'
	}
}

// Supporting interfaces
export interface ValidationResult {
	isValid: boolean
	errors: string[]
	warnings: string[]
}

export interface EmissionSummary {
	scope1: number
	scope2: number
	scope3: number
	total: number
}

export interface EnergyConsumptionSummary {
	kwh: number
	liter: number
	renewable: number
	nonRenewable: number
	nuclear: number
	stationaryCombustion: number
	mobileCombustion: number
}

export interface TransactionDisplayData {
	id: string
	date: string
	supplier: string
	amount: number
	description: string
	status: string
	totalEmissions: number
	energyConsumption: number
	scope1: number
	scope2: number
	scope3: number
}