// Common utility functions used across services

export class ValidationUtils {
	static isValidEmail(email: string): boolean {
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
		return emailRegex.test(email)
	}

	static isValidObjectId(id: string): boolean {
		return /^[0-9a-fA-F]{24}$/.test(id)
	}

	static sanitizeString(str: string): string {
		return str.trim().replace(/[<>]/g, '')
	}

	static validateRequired(obj: any, requiredFields: string[]): string[] {
		const missingFields: string[] = []
		requiredFields.forEach(field => {
			if (!obj[field] && obj[field] !== 0 && obj[field] !== false) {
				missingFields.push(field)
			}
		})
		return missingFields
	}
}

export class DateUtils {
	static formatDate(date: Date): string {
		return date.toISOString().split('T')[0]
	}

	static startOfDay(date: Date): Date {
		const start = new Date(date)
		start.setHours(0, 0, 0, 0)
		return start
	}

	static endOfDay(date: Date): Date {
		const end = new Date(date)
		end.setHours(23, 59, 59, 999)
		return end
	}

	static addDays(date: Date, days: number): Date {
		const result = new Date(date)
		result.setDate(result.getDate() + days)
		return result
	}

	static getDaysBetween(startDate: Date, endDate: Date): number {
		const diffTime = Math.abs(endDate.getTime() - startDate.getTime())
		return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
	}

	static isValidDateRange(startDate: string, endDate: string): boolean {
		const start = new Date(startDate)
		const end = new Date(endDate)
		return start.getTime() <= end.getTime()
	}
}

export class NumberUtils {
	static roundToDecimals(num: number, decimals: number = 2): number {
		return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals)
	}

	static isPositive(num: number): boolean {
		return num > 0
	}

	static clamp(num: number, min: number, max: number): number {
		return Math.min(Math.max(num, min), max)
	}

	static calculatePercentage(part: number, total: number): number {
		if (total === 0) return 0
		return this.roundToDecimals((part / total) * 100)
	}
}

export class ResponseUtils {
	static success<T>(data: T, message?: string) {
		return {
			success: true,
			data,
			message
		}
	}

	static error(message: string, code?: string) {
		return {
			success: false,
			error: message,
			code
		}
	}

	static paginated<T>(data: T[], total: number, page: number, limit: number) {
		return {
			data,
			total,
			page,
			limit,
			totalPages: Math.ceil(total / limit),
			hasNext: page * limit < total,
			hasPrev: page > 1
		}
	}
}

export class EmissionCalculator {
	// Common emission factors (kg CO2e per unit)
	static readonly EMISSION_FACTORS = {
		electricity_grid: 0.5, // kg CO2e per kWh (average grid)
		natural_gas: 0.185, // kg CO2e per kWh
		gasoline: 2.31, // kg CO2e per liter
		diesel: 2.68, // kg CO2e per liter
		coal: 0.94, // kg CO2e per kWh
	}

	static calculateEmissions(consumption: number, emissionFactor: number): number {
		return NumberUtils.roundToDecimals(consumption * emissionFactor)
	}

	static getEmissionFactor(sourceType: string): number {
		return this.EMISSION_FACTORS[sourceType as keyof typeof this.EMISSION_FACTORS] || 0
	}

	static calculateScope2Emissions(electricityConsumption: number, gridFactor?: number): number {
		const factor = gridFactor || this.EMISSION_FACTORS.electricity_grid
		return this.calculateEmissions(electricityConsumption, factor)
	}

	static calculateTotalEmissions(energyRecords: Array<{ consumption: number, emissionFactor: number }>): number {
		const total = energyRecords.reduce((sum, record) => {
			return sum + this.calculateEmissions(record.consumption, record.emissionFactor)
		}, 0)
		return NumberUtils.roundToDecimals(total)
	}
}

export class ErrorMessages {
	static readonly VALIDATION = {
		REQUIRED_FIELD: (field: string) => `${field} is required`,
		INVALID_EMAIL: 'Invalid email format',
		INVALID_DATE: 'Invalid date format',
		INVALID_DATE_RANGE: 'End date must be after start date',
		INVALID_OBJECT_ID: 'Invalid ID format',
		POSITIVE_NUMBER: (field: string) => `${field} must be a positive number`
	}

	static readonly NOT_FOUND = {
		USER: 'User not found',
		TRANSACTION: 'Transaction not found',
		ENERGY_RECORD: 'Energy record not found',
		SCOPE_CATEGORY: 'Scope category not found'
	}

	static readonly DATABASE = {
		CONNECTION_FAILED: 'Database connection failed',
		OPERATION_FAILED: 'Database operation failed',
		DUPLICATE_ENTRY: 'Duplicate entry found'
	}
}