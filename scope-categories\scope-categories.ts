import { api } from "encore.dev/api"
import { secret } from "encore.dev/config"
import { ScopeCategory, ListScopeCategoriesResponse, PaginationQuery, EmissionScope } from "../shared/types"
import { DatabaseConnection, DatabaseHelpers } from "../shared/database"

// Secret must be defined at module level within a service
const mongoUri = secret("MONGODB_URI")

interface ScopeCategoriesQuery extends PaginationQuery {
	scope?: EmissionScope
	isActive?: boolean
}

// GET /scope-categories - List scope categories with filtering
export const listScopeCategories = api(
	{ method: "GET", path: "/scope-categories" }, 
	async (query: ScopeCategoriesQuery): Promise<ListScopeCategoriesResponse> => {
		const db = await DatabaseConnection.getDb(mongoUri())
		const { page, limit, skip } = DatabaseHelpers.calculatePagination(query.page, query.limit)
		
		// Build filter query
		const filter: any = {}
		if (query.scope) {
			filter.scope = query.scope
		}
		if (query.isActive !== undefined) {
			filter.isActive = query.isActive
		}
		
		const projection = DatabaseHelpers.createProjection([
			'_id', 'scope', 'category', 'description', 'emissionFactor', 'unit', 'isActive', 'createdAt'
		])
		
		const [categoryDocs, total] = await Promise.all([
			db.collection("scope_categories").find(filter, { projection }).skip(skip).limit(limit).toArray(),
			db.collection("scope_categories").countDocuments(filter)
		])
		
		const scopeCategories: ScopeCategory[] = categoryDocs.map(doc => 
			DatabaseHelpers.convertMongoDoc<ScopeCategory>(doc, [
				'_id', 'scope', 'category', 'description', 'emissionFactor', 'unit', 'isActive', 'createdAt'
			])
		)
		
		return { scopeCategories, total, page, limit }
	}
)

interface CreateScopeCategoryRequest {
	scope: EmissionScope
	category: string
	description?: string
	emissionFactor: number
	unit: string
	isActive?: boolean
}

interface CreateScopeCategoryResponse {
	scopeCategory: ScopeCategory
	success: boolean
}

// POST /scope-categories - Create a new scope category
export const createScopeCategory = api(
	{ method: "POST", path: "/scope-categories" },
	async (req: CreateScopeCategoryRequest): Promise<CreateScopeCategoryResponse> => {
		const db = await DatabaseConnection.getDb(mongoUri())
		
		const scopeCategory = {
			...req,
			isActive: req.isActive !== undefined ? req.isActive : true,
			createdAt: new Date(),
			updatedAt: new Date()
		}
		
		const result = await db.collection("scope_categories").insertOne(scopeCategory)
		
		const createdCategory = DatabaseHelpers.convertMongoDoc<ScopeCategory>(
			{ ...scopeCategory, _id: result.insertedId },
			['_id', 'scope', 'category', 'description', 'emissionFactor', 'unit', 'isActive', 'createdAt', 'updatedAt']
		)
		
		return {
			scopeCategory: createdCategory,
			success: true
		}
	}
)

interface UpdateScopeCategoryRequest {
	id: string
	category?: string
	description?: string
	emissionFactor?: number
	unit?: string
	isActive?: boolean
}

interface UpdateScopeCategoryResponse {
	scopeCategory: ScopeCategory | null
	success: boolean
	found: boolean
}

// PUT /scope-categories/:id - Update a scope category
export const updateScopeCategory = api(
	{ method: "PUT", path: "/scope-categories/:id" },
	async ({ id, ...updates }: UpdateScopeCategoryRequest): Promise<UpdateScopeCategoryResponse> => {
		const db = await DatabaseConnection.getDb(mongoUri())
		
		// Remove undefined values from updates
		const cleanUpdates: any = Object.fromEntries(
			Object.entries(updates).filter(([_, value]) => value !== undefined)
		)
		
		if (Object.keys(cleanUpdates).length === 0) {
			return { scopeCategory: null, success: false, found: false }
		}
		
		cleanUpdates.updatedAt = new Date()
		
		const result = await db.collection("scope_categories").findOneAndUpdate(
			{ _id: new (await import('mongodb')).ObjectId(id) },
			{ $set: cleanUpdates },
			{ returnDocument: 'after' }
		)
		
		if (!result || !result.value) {
			return { scopeCategory: null, success: false, found: false }
		}
		
		const scopeCategory = DatabaseHelpers.convertMongoDoc<ScopeCategory>(result.value, [
			'_id', 'scope', 'category', 'description', 'emissionFactor', 'unit', 'isActive', 'createdAt', 'updatedAt'
		])
		
		return { scopeCategory, success: true, found: true }
	}
)

interface GetScopeCategoryByIdRequest {
	id: string
}

interface GetScopeCategoryResponse {
	scopeCategory: ScopeCategory | null
	found: boolean
}

// GET /scope-categories/:id - Get a specific scope category
export const getScopeCategoryById = api(
	{ method: "GET", path: "/scope-categories/:id" },
	async ({ id }: GetScopeCategoryByIdRequest): Promise<GetScopeCategoryResponse> => {
		const db = await DatabaseConnection.getDb(mongoUri())
		
		const projection = DatabaseHelpers.createProjection([
			'_id', 'scope', 'category', 'description', 'emissionFactor', 'unit', 'isActive', 'createdAt', 'updatedAt'
		])
		
		const categoryDoc = await db.collection("scope_categories").findOne(
			{ _id: new (await import('mongodb')).ObjectId(id) }, 
			{ projection }
		)
		
		if (!categoryDoc) {
			return { scopeCategory: null, found: false }
		}
		
		const scopeCategory = DatabaseHelpers.convertMongoDoc<ScopeCategory>(categoryDoc, [
			'_id', 'scope', 'category', 'description', 'emissionFactor', 'unit', 'isActive', 'createdAt', 'updatedAt'
		])
		
		return { scopeCategory, found: true }
	}
)

// GET /scope-categories/by-scope/:scope - Get categories by emission scope
export const getCategoriesByScope = api(
	{ method: "GET", path: "/scope-categories/by-scope/:scope" },
	async ({ scope }: { scope: string }): Promise<ListScopeCategoriesResponse> => {
		const db = await DatabaseConnection.getDb(mongoUri())
		
		// Validate scope parameter
		const validScopes = ['scope1', 'scope2', 'scope3']
		if (!validScopes.includes(scope)) {
			throw new Error(`Invalid scope. Must be one of: ${validScopes.join(', ')}`)
		}
		
		const filter = { scope: scope as EmissionScope, isActive: true }
		const projection = DatabaseHelpers.createProjection([
			'_id', 'scope', 'category', 'description', 'emissionFactor', 'unit', 'isActive'
		])
		
		const [categoryDocs, total] = await Promise.all([
			db.collection("scope_categories").find(filter, { projection }).toArray(),
			db.collection("scope_categories").countDocuments(filter)
		])
		
		const scopeCategories: ScopeCategory[] = categoryDocs.map(doc => 
			DatabaseHelpers.convertMongoDoc<ScopeCategory>(doc, [
				'_id', 'scope', 'category', 'description', 'emissionFactor', 'unit', 'isActive'
			])
		)
		
		return { scopeCategories, total }
	}
)