# Transaction Types Documentation

This directory contains comprehensive TypeScript types for the transaction schema, organized into specialized modules for better maintainability and clarity.

## 📁 Structure

```
transaction/
├── index.ts          # Core transaction interfaces
├── emissions.ts      # Emission calculation types
├── utils.ts          # Validation and utility functions
├── main.ts           # Main export file
└── README.md         # This documentation
```

## 🏗️ Core Types

### `Transaction` (index.ts)
The main transaction interface based on the complete MongoDB schema with all optional fields:

```typescript
interface Transaction {
  _id?: string
  AccountDescription?: string
  AccountID?: string
  Amount?: number
  TransactionDate?: string
  SupplierName?: string
  Lines?: TransactionLine[]
  // ... and many more fields
}
```

### `TransactionLine` (index.ts)
Individual line items within a transaction:

```typescript
interface TransactionLine {
  AccountID?: string
  CreditAmount?: MoneyAmount
  DebitAmount?: MoneyAmount
  Analysis?: Analysis[]
  // ... additional fields
}
```

## 🌿 Emission Types (emissions.ts)

### Scope-Specific Data Structures
- `Scope1EmissionData` - Direct emissions (combustion, vehicles, fugitive)
- `Scope2EmissionData` - Indirect emissions (electricity, heat)
- `Scope3EmissionData` - Other indirect emissions

### Calculation Rows
Detailed calculation data for each emission scope:
- `Scope1CombustionRow` - Stationary combustion data
- `Scope1VehicleRow` - Mobile combustion/vehicle data
- `Scope2ElectricityRow` - Electricity consumption data
- And many more specialized calculation types...

### Emission Factors
```typescript
interface EmissionFactor {
  name: string
  value: number
  unit: string
  source: string
  dataQuality: DataQualityRating
}
```

## 🛠️ Utilities (utils.ts)

### TransactionValidation
Comprehensive validation for transaction data:
```typescript
TransactionValidation.validateTransaction(transaction)
// Returns: { isValid: boolean, errors: string[], warnings: string[] }
```

### TransactionCalculations  
Business logic calculations:
```typescript
TransactionCalculations.calculateTotalEmissions(transaction)
TransactionCalculations.calculateEnergyConsumption(transaction)
TransactionCalculations.calculateEmissionIntensity(transaction)
```

### TransactionHelpers
Utility functions for transaction management:
```typescript
TransactionHelpers.createEmptyTransaction(supplierID)
TransactionHelpers.generateTransactionId()
TransactionHelpers.formatTransactionForDisplay(transaction)
```

## 📊 Usage Examples

### Creating a New Transaction
```typescript
import { TransactionHelpers, Transaction } from '../shared/types/transaction/main'

// Create empty transaction
const newTransaction = TransactionHelpers.createEmptyTransaction('SUPPLIER_123')

// Add data
const transaction: Transaction = {
  ...newTransaction,
  Amount: 1500.00,
  Description: ['Office supplies', 'Paper and stationery'],
  Scope_1: 25.5,
  Scope_2: 12.3,
  kwh: 150
}
```

### Validating Transaction Data
```typescript
import { TransactionValidation } from '../shared/types/transaction/main'

const validation = TransactionValidation.validateTransaction(transaction)

if (!validation.isValid) {
  console.error('Validation errors:', validation.errors)
  console.warn('Warnings:', validation.warnings)
}
```

### Calculating Emissions
```typescript
import { TransactionCalculations } from '../shared/types/transaction/main'

const emissions = TransactionCalculations.calculateTotalEmissions(transaction)
console.log(`Total emissions: ${emissions.total} kg CO2e`)

const energy = TransactionCalculations.calculateEnergyConsumption(transaction)
console.log(`Energy consumed: ${energy.kwh} kWh`)
```

### Working with Emission Calculations
```typescript
import { 
  EmissionCalculationRequest,
  Scope3CalculationMethod,
  DataQualityRating 
} from '../shared/types/transaction/main'

const calculationRequest: EmissionCalculationRequest = {
  transactionId: 'TXN_123',
  calculationMethod: Scope3CalculationMethod.SPEND_BASED,
  includeUncertainty: true
}
```

## 🎯 Key Features

### 1. **Complete Schema Coverage**
- All fields from the original MongoDB schema
- Proper TypeScript typing for each field
- Optional fields marked appropriately

### 2. **Emission Scope Support**
- Detailed Scope 1, 2, 3 emission tracking
- Calculation rows for different emission sources
- Energy consumption tracking (kWh, liters, etc.)

### 3. **Business Logic**
- Transaction validation with detailed error reporting
- Emission calculations and energy consumption analysis
- Display formatting for UI components

### 4. **Extensibility**
- Modular structure allows easy extension
- Clear separation between core data, calculations, and utilities
- Type-safe throughout with full IntelliSense support

## 🔗 Integration

### In Services
```typescript
import { 
  Transaction, 
  ListTransactionsResponse, 
  TransactionValidation 
} from '../shared/types/transaction/main'

export const createTransaction = api(
  { method: "POST", path: "/transactions" },
  async (req: Transaction): Promise<{ success: boolean }> => {
    const validation = TransactionValidation.validateTransaction(req)
    
    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.join(', ')}`)
    }
    
    // Process transaction...
    return { success: true }
  }
)
```

### In Database Operations
```typescript
import { Transaction } from '../shared/types/transaction/main'
import { DatabaseHelpers } from '../shared/database'

const transactionDocs = await db.collection('transactions').find({}).toArray()
const transactions: Transaction[] = transactionDocs.map(doc => 
  DatabaseHelpers.convertMongoDoc<Transaction>(doc, [
    '_id', 'TransactionID', 'Amount', 'TransactionDate', 'SupplierName'
  ])
)
```

## 🚀 Benefits

1. **Type Safety** - Full TypeScript coverage prevents runtime errors
2. **Maintainability** - Organized structure makes code easy to maintain  
3. **Reusability** - Shared types ensure consistency across services
4. **Validation** - Built-in validation prevents invalid data
5. **Business Logic** - Centralized calculations ensure accuracy
6. **Documentation** - Self-documenting code with clear interfaces

This comprehensive transaction type system provides a robust foundation for building emission tracking and carbon accounting applications with Encore.dev!